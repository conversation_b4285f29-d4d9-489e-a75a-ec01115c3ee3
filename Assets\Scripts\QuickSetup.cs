using UnityEngine;
#if UNITY_EDITOR
using UnityEditor;
#endif

public class QuickSetup : MonoBehaviour
{
    [Header("Quick Setup")]
    [Tooltip("勾选后运行游戏会自动创建第一人称玩家")]
    public bool createPlayerInScene = true;
    
    void Start()
    {
        if (createPlayerInScene)
        {
            CreateFirstPersonPlayer();
        }
    }
    
    [ContextMenu("Create First Person Player")]
    public void CreateFirstPersonPlayer()
    {
        // 检查是否已经有玩家
        GameObject existingPlayer = GameObject.FindWithTag("Player");
        if (existingPlayer != null)
        {
            Debug.Log("场景中已经存在玩家，删除旧的玩家...");
            DestroyImmediate(existingPlayer);
        }
        
        // 删除现有的Main Camera
        Camera[] cameras = FindObjectsOfType<Camera>();
        foreach (Camera cam in cameras)
        {
            if (cam.gameObject.name == "Main Camera")
            {
                Debug.Log("删除原有的Main Camera...");
                DestroyImmediate(cam.gameObject);
            }
        }
        
        // 创建玩家GameObject
        GameObject player = new GameObject("FirstPersonPlayer");
        player.tag = "Player";
        player.transform.position = new Vector3(0, 2, 0);
        
        // 添加CharacterController
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.height = 2f;
        controller.radius = 0.5f;
        controller.center = new Vector3(0, 1, 0);
        controller.slopeLimit = 45f;
        controller.stepOffset = 0.3f;
        
        // 创建摄像机
        GameObject cameraObj = new GameObject("PlayerCamera");
        cameraObj.transform.SetParent(player.transform);
        cameraObj.transform.localPosition = new Vector3(0, 1.6f, 0);
        cameraObj.tag = "MainCamera";
        
        Camera camera = cameraObj.AddComponent<Camera>();
        camera.fieldOfView = 75f;
        camera.nearClipPlane = 0.3f;
        camera.farClipPlane = 1000f;
        
        // 添加AudioListener
        cameraObj.AddComponent<AudioListener>();
        
        // 创建地面检测点
        GameObject groundCheckObj = new GameObject("GroundCheck");
        groundCheckObj.transform.SetParent(player.transform);
        groundCheckObj.transform.localPosition = new Vector3(0, -1f, 0);
        
        // 添加FirstPersonController脚本
        FirstPersonController fpController = player.AddComponent<FirstPersonController>();
        
        // 设置地面检测引用
        fpController.groundCheck = groundCheckObj.transform;
        
        Debug.Log("✅ 第一人称玩家创建成功！");
        Debug.Log("控制方式：WASD移动，空格跳跃，鼠标控制视角，ESC切换鼠标锁定");
        
        // 创建简单的地面
        CreateSimpleGround();
    }
    
    void CreateSimpleGround()
    {
        // 检查是否已经有地面
        GameObject existingGround = GameObject.Find("Ground");
        if (existingGround == null)
        {
            GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
            ground.name = "Ground";
            ground.transform.position = Vector3.zero;
            ground.transform.localScale = new Vector3(10, 1, 10);
            
            // 创建简单的材质
            Material groundMat = new Material(Shader.Find("Standard"));
            groundMat.color = new Color(0.5f, 0.5f, 0.5f);
            ground.GetComponent<Renderer>().material = groundMat;
            
            Debug.Log("✅ 地面创建成功！");
        }
        
        // 创建一些简单的障碍物
        CreateSimpleObstacles();
    }
    
    void CreateSimpleObstacles()
    {
        // 创建几个简单的立方体作为障碍物
        Vector3[] positions = {
            new Vector3(5, 0.5f, 5),
            new Vector3(-8, 0.5f, 3),
            new Vector3(3, 0.5f, -7),
            new Vector3(-5, 0.5f, -5)
        };
        
        for (int i = 0; i < positions.Length; i++)
        {
            GameObject obstacle = GameObject.CreatePrimitive(PrimitiveType.Cube);
            obstacle.name = "Obstacle_" + (i + 1);
            obstacle.transform.position = positions[i];
            obstacle.transform.localScale = new Vector3(1, 1, 1);
            
            // 随机颜色
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = new Color(Random.Range(0.3f, 1f), Random.Range(0.3f, 1f), Random.Range(0.3f, 1f));
            obstacle.GetComponent<Renderer>().material = mat;
        }
        
        Debug.Log("✅ 障碍物创建成功！");
    }
    
    [ContextMenu("Create Simple Scene")]
    public void CreateCompleteScene()
    {
        CreateFirstPersonPlayer();
        
        // 设置光照
        Light[] lights = FindObjectsOfType<Light>();
        if (lights.Length == 0)
        {
            GameObject lightObj = new GameObject("Directional Light");
            Light light = lightObj.AddComponent<Light>();
            light.type = LightType.Directional;
            light.intensity = 1f;
            light.shadows = LightShadows.Soft;
            lightObj.transform.rotation = Quaternion.Euler(45f, 45f, 0f);
            
            Debug.Log("✅ 光照设置完成！");
        }
        
        Debug.Log("🎮 完整场景创建完成！现在可以点击Play按钮开始游戏了！");
    }
}

#if UNITY_EDITOR
[CustomEditor(typeof(QuickSetup))]
public class QuickSetupEditor : Editor
{
    public override void OnInspectorGUI()
    {
        DrawDefaultInspector();

        QuickSetup setup = (QuickSetup)target;

        GUILayout.Space(10);

        if (GUILayout.Button("🎮 创建第一人称玩家", GUILayout.Height(30)))
        {
            setup.CreateFirstPersonPlayer();
        }

        if (GUILayout.Button("🌍 创建完整场景", GUILayout.Height(30)))
        {
            setup.CreateCompleteScene();
        }

        GUILayout.Space(10);
        GUILayout.Label("使用说明：", EditorStyles.boldLabel);
        GUILayout.Label("1. 点击上面的按钮创建玩家和场景");
        GUILayout.Label("2. 或者勾选'createPlayerInScene'然后运行游戏");
        GUILayout.Label("3. 控制：WASD移动，空格跳跃，鼠标视角");
    }
}
#endif
