using UnityEngine;

public class SceneSetup : MonoBehaviour
{
    [Header("Scene Generation")]
    public bool generateOnStart = true;
    public Material groundMaterial;
    public Material wallMaterial;
    
    void Start()
    {
        if (generateOnStart)
        {
            CreateBasicScene();
        }
    }
    
    [ContextMenu("Create Basic Scene")]
    public void CreateBasicScene()
    {
        // Create ground
        CreateGround();
        
        // Create some walls/obstacles
        CreateWalls();
        
        // Create player if not exists
        CreatePlayer();
        
        // Setup lighting
        SetupLighting();
    }
    
    void CreateGround()
    {
        GameObject ground = GameObject.CreatePrimitive(PrimitiveType.Plane);
        ground.name = "Ground";
        ground.transform.position = Vector3.zero;
        ground.transform.localScale = new Vector3(10, 1, 10); // 100x100 units
        
        // Apply material if available
        if (groundMaterial != null)
        {
            ground.GetComponent<Renderer>().material = groundMaterial;
        }
        else
        {
            // Create a simple gray material
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = Color.gray;
            ground.GetComponent<Renderer>().material = mat;
        }
        
        // Set layer to Ground
        ground.layer = LayerMask.NameToLayer("Default");
    }
    
    void CreateWalls()
    {
        // Create some walls around the area
        CreateWall(new Vector3(0, 2.5f, 50), new Vector3(100, 5, 1)); // North wall
        CreateWall(new Vector3(0, 2.5f, -50), new Vector3(100, 5, 1)); // South wall
        CreateWall(new Vector3(50, 2.5f, 0), new Vector3(1, 5, 100)); // East wall
        CreateWall(new Vector3(-50, 2.5f, 0), new Vector3(1, 5, 100)); // West wall
        
        // Create some obstacles
        CreateObstacle(new Vector3(10, 1, 10), new Vector3(2, 2, 2));
        CreateObstacle(new Vector3(-15, 1, 5), new Vector3(3, 2, 1));
        CreateObstacle(new Vector3(5, 1, -20), new Vector3(1, 2, 4));
        CreateObstacle(new Vector3(-8, 1, -12), new Vector3(2, 2, 2));
    }
    
    void CreateWall(Vector3 position, Vector3 scale)
    {
        GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        wall.name = "Wall";
        wall.transform.position = position;
        wall.transform.localScale = scale;
        
        // Apply material if available
        if (wallMaterial != null)
        {
            wall.GetComponent<Renderer>().material = wallMaterial;
        }
        else
        {
            // Create a simple brown material
            Material mat = new Material(Shader.Find("Standard"));
            mat.color = new Color(0.6f, 0.4f, 0.2f); // Brown color
            wall.GetComponent<Renderer>().material = mat;
        }
    }
    
    void CreateObstacle(Vector3 position, Vector3 scale)
    {
        GameObject obstacle = GameObject.CreatePrimitive(PrimitiveType.Cube);
        obstacle.name = "Obstacle";
        obstacle.transform.position = position;
        obstacle.transform.localScale = scale;
        
        // Create a random colored material
        Material mat = new Material(Shader.Find("Standard"));
        mat.color = new Color(Random.Range(0.3f, 1f), Random.Range(0.3f, 1f), Random.Range(0.3f, 1f));
        obstacle.GetComponent<Renderer>().material = mat;
    }
    
    void CreatePlayer()
    {
        // Check if player already exists
        if (GameObject.FindWithTag("Player") != null)
        {
            Debug.Log("Player already exists in scene");
            return;
        }
        
        // Create player GameObject
        GameObject player = new GameObject("Player");
        player.tag = "Player";
        player.transform.position = new Vector3(0, 2, 0);
        
        // Add CharacterController
        CharacterController controller = player.AddComponent<CharacterController>();
        controller.height = 2f;
        controller.radius = 0.5f;
        controller.center = new Vector3(0, 1, 0);
        
        // Create camera
        GameObject cameraObj = new GameObject("PlayerCamera");
        cameraObj.transform.SetParent(player.transform);
        cameraObj.transform.localPosition = new Vector3(0, 1.6f, 0);
        
        Camera camera = cameraObj.AddComponent<Camera>();
        camera.fieldOfView = 75f;
        
        // Add audio listener
        cameraObj.AddComponent<AudioListener>();
        
        // Add FirstPersonController script
        player.AddComponent<FirstPersonController>();
        
        Debug.Log("Player created successfully!");
    }
    
    void SetupLighting()
    {
        // Create directional light if none exists
        Light[] lights = FindObjectsOfType<Light>();
        bool hasDirectionalLight = false;
        
        foreach (Light light in lights)
        {
            if (light.type == LightType.Directional)
            {
                hasDirectionalLight = true;
                break;
            }
        }
        
        if (!hasDirectionalLight)
        {
            GameObject lightObj = new GameObject("Directional Light");
            Light light = lightObj.AddComponent<Light>();
            light.type = LightType.Directional;
            light.intensity = 1f;
            light.shadows = LightShadows.Soft;
            lightObj.transform.rotation = Quaternion.Euler(45f, 45f, 0f);
        }
        
        // Set ambient lighting
        RenderSettings.ambientMode = UnityEngine.Rendering.AmbientMode.Trilight;
        RenderSettings.ambientSkyColor = new Color(0.5f, 0.7f, 1f);
        RenderSettings.ambientEquatorColor = new Color(0.4f, 0.4f, 0.4f);
        RenderSettings.ambientGroundColor = new Color(0.2f, 0.2f, 0.2f);
    }
}
