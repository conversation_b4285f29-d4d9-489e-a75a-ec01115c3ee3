# Unity 第一人称控制器

这是一个基础的Unity 3D第一人称角色控制器项目。

## 功能特性

### 角色控制
- **WASD移动**: W前进，S后退，A左移，D右移
- **空格跳跃**: 按空格键跳跃
- **Shift奔跑**: 按住左Shift键奔跑
- **鼠标视角**: 鼠标控制视角转动
- **ESC键**: 切换鼠标锁定状态

### 物理特性
- 重力系统
- 地面检测
- 角色碰撞
- 平滑的鼠标视角控制

## 文件结构

```
Assets/
├── Scripts/
│   ├── FirstPersonController.cs    # 第一人称控制器脚本
│   └── SceneSetup.cs              # 场景自动生成脚本
├── Materials/
│   └── GroundMaterial.mat         # 地面材质
├── Prefabs/
│   └── FirstPersonPlayer.prefab   # 第一人称玩家预制体
└── Scenes/
    └── SampleScene.scene          # 示例场景
```

## 快速开始

### 方法1: 使用预制体
1. 打开Unity编辑器
2. 将 `Assets/Prefabs/FirstPersonPlayer.prefab` 拖拽到场景中
3. 删除场景中原有的Main Camera（如果有的话）
4. 运行游戏

### 方法2: 使用场景自动生成
1. 在场景中创建一个空的GameObject
2. 添加 `SceneSetup` 脚本到这个GameObject上
3. 运行游戏，脚本会自动创建完整的场景和玩家

## 控制器参数说明

### FirstPersonController 脚本参数

#### Movement Settings (移动设置)
- **Walk Speed**: 行走速度 (默认: 5)
- **Run Speed**: 奔跑速度 (默认: 8)
- **Jump Height**: 跳跃高度 (默认: 2)
- **Gravity**: 重力强度 (默认: -9.81)

#### Mouse Look Settings (鼠标视角设置)
- **Mouse Sensitivity**: 鼠标灵敏度 (默认: 2)
- **Max Look Angle**: 最大仰俯角度 (默认: 80度)

#### Ground Check (地面检测)
- **Ground Check**: 地面检测点的Transform
- **Ground Distance**: 地面检测距离 (默认: 0.4)
- **Ground Mask**: 地面层级遮罩

## 自定义和扩展

### 添加新功能
你可以轻松扩展这个控制器：

1. **添加音效**: 在脚本中添加AudioSource组件和音效
2. **添加动画**: 集成角色动画系统
3. **添加UI**: 创建血量、体力等UI元素
4. **添加武器**: 实现射击或近战系统

### 调整参数
- 在Inspector面板中调整各种参数来获得理想的手感
- 修改鼠标灵敏度来适应不同玩家的偏好
- 调整移动速度和跳跃高度来改变游戏感觉

## 注意事项

1. 确保场景中有地面对象，并且它们在正确的Layer上
2. 如果跳跃不工作，检查Ground Check的位置和Ground Mask设置
3. 如果鼠标视角有问题，确保游戏运行时鼠标被正确锁定
4. 建议在Build Settings中添加场景并测试

## 故障排除

### 常见问题
- **角色穿过地面**: 检查CharacterController的设置和地面碰撞体
- **无法跳跃**: 确保Ground Check正确设置并且地面在正确的Layer上
- **鼠标视角不工作**: 检查鼠标锁定状态和灵敏度设置
- **移动太快/太慢**: 调整walkSpeed和runSpeed参数

### 调试技巧
- 在Scene视图中可以看到Ground Check的绿色/红色球体指示器
- 使用Console查看调试信息
- 在Inspector中实时调整参数来测试效果

## 版本信息
- Unity版本: 2022.3 LTS 或更高
- 脚本语言: C#
- 渲染管线: 内置渲染管线

祝你游戏开发愉快！
