{"name": "com.unity.performance.profile-analyzer", "displayName": "Profile Analyzer", "version": "1.2.3", "description": "The Profile Analyzer tool supports the standard Unity Profiler. You can use it to analyze multiple frames and multiple data sets of the CPU data in the Profiler.\n\nMain features: \n▪ Multi-frame analysis of a single set of Profiler CPU data \n▪ Comparison of two multi-frame profile scans \n\n", "unity": "2020.3", "dependencies": {}, "_upm": {"changelog": "### Fixed\n\n* Fixed minimum supported version in documentation.\n* Fixed PROFB-199; Unchecking 'Hide Removed Markers' doesn't work."}, "upmCi": {"footprint": "d79e16167f5d8d452ace6ad23470b78fcfd41d08"}, "documentationUrl": "https://docs.unity3d.com/Packages/com.unity.performance.profile-analyzer@1.2/manual/index.html", "repository": {"url": "https://github.cds.internal.unity3d.com/unity/com.unity.performance.profile-analyzer.git", "type": "git", "revision": "835e61d94bd201d0dea5763dff75e86b6b61de29"}}